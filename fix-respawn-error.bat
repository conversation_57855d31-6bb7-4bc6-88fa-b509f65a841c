@echo off
echo ========================================
echo  Minecraft Server - Fix Respawn Error
echo ========================================
echo.
echo Este script ayuda a solucionar el error de ConcurrentModificationException
echo que ocurre al reaparecer despues de morir.
echo.
echo Opciones disponibles:
echo 1. Deshabilitar efectos de Apocalypse Now (RECOMENDADO)
echo 2. Restaurar efectos de Apocalypse Now
echo 3. Crear respaldo de configuracion
echo 4. Restaurar desde respaldo
echo 5. Salir
echo.
set /p choice="Selecciona una opcion (1-5): "

if "%choice%"=="1" goto disable_effects
if "%choice%"=="2" goto enable_effects
if "%choice%"=="3" goto backup_config
if "%choice%"=="4" goto restore_config
if "%choice%"=="5" goto exit

:disable_effects
echo Deshabilitando efectos de Apocalypse Now...
powershell -Command "(Get-Content 'config\Apocalypse Now Config.toml') -replace '\"Enables mod effects\" = true', '\"Enables mod effects\" = false' | Set-Content 'config\Apocalypse Now Config.toml'"
echo Efectos deshabilitados. Reinicia el servidor para aplicar cambios.
pause
goto menu

:enable_effects
echo Habilitando efectos de Apocalypse Now...
powershell -Command "(Get-Content 'config\Apocalypse Now Config.toml') -replace '\"Enables mod effects\" = false', '\"Enables mod effects\" = true' | Set-Content 'config\Apocalypse Now Config.toml'"
echo Efectos habilitados. Reinicia el servidor para aplicar cambios.
pause
goto menu

:backup_config
echo Creando respaldo de configuracion...
if not exist "config\backup" mkdir "config\backup"
copy "config\Apocalypse Now Config.toml" "config\backup\Apocalypse Now Config.toml.backup"
echo Respaldo creado en config\backup\
pause
goto menu

:restore_config
echo Restaurando configuracion desde respaldo...
if exist "config\backup\Apocalypse Now Config.toml.backup" (
    copy "config\backup\Apocalypse Now Config.toml.backup" "config\Apocalypse Now Config.toml"
    echo Configuracion restaurada.
) else (
    echo No se encontro archivo de respaldo.
)
pause
goto menu

:exit
echo Saliendo...
exit

:menu
cls
goto start
