#If this is set to true you get an obituary after you died
enable_obituary = true
#The blocks that can be replaced with a grave
#If it starts with '#' it is a tag
replaceable_blocks = ["#gravestone:grave_replaceable"]
#If this is set to true the obituary will be taken out of your inventory when you break the grave
remove_obituary = false
#If this is set to true only the player that owns the grave and admins can break the grave
only_owners_can_break = false
#If this is set to true the ghost of the dead player will be spawned when the grave is broken
spawn_ghost = false
#If this is set to true the ghost player will defend the player
friendly_ghost = true
#If this is set to true you get your items back into your inventory by sneaking on the grave
sneak_pickup = false
#If this is set to true you get your items sorted back into your inventory by breaking the grave
break_pickup = true
#If this is set to true the grave will replace other blocks if there is no free space above
#Note that this might cause issues with other mods or multiblock structures - This option is not recommended and subject to change
strict_placement = false

